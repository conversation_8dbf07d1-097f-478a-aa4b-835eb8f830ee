/**
 *Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.trade.reimportpaychkfile;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @api {post} dubbo:com.howbuy.tms.high.batch.facade.trade.reimportpaychkfile.
 *      HighReimportPayChkFileFacade.execute() Z920003-重新导入支付对账文件
 * @apiGroup high-batch-center
 * @apiDescription 重新导入支付对账文件，删除当天已导入的数据并重新导入
 * 
 * @apiUse batchBaseRequest
 * 
 * @apiParam {String} taTradeDt TA交易日期，格式YYYYMMDD
 * @apiParam {String} [sysCode] 系统代码，90-高端，91-普通公募
 * @apiParam {String} [forceReimport=N] 是否强制重新导入，Y-强制重新导入，N-检查状态后导入
 * 
 * @apiParamExample {json} Request Example dubbo
 *                  com.howbuy.tms.high.batch.facade.trade.reimportpaychkfile.
 *                  HighReimportPayChkFileRequest
 * 
 * @apiUse batchBaseResponse
 * 
 * @apiSuccess {List} processMessages 处理结果消息列表
 * @apiSuccess {Integer} deletedBankRecordCount 删除的银行卡支付对账记录数
 * @apiSuccess {Integer} deletedCxgRecordCount 删除的储蓄罐支付对账记录数
 * @apiSuccess {Integer} reimportedBankRecordCount 重新导入的银行卡支付对账记录数
 * @apiSuccess {Integer} reimportedCxgRecordCount 重新导入的储蓄罐支付对账记录数
 * @apiSuccess {List} rolledBackNodes 回滚的批处理节点状态列表
 * 
 * @apiSuccessExample {json} Response Example 
 *                  dubbo com.howbuy.tms.high.batch.facade.trade.reimportpaychkfile.
 *                  HighReimportPayChkFileResponse
 * 
 * @description: 重新导入支付对账文件接口
 * <AUTHOR>
 * @date 2025/01/23 10:00:00
 * @since JDK 1.8
 */
public interface HighReimportPayChkFileFacade extends BaseFacade<HighReimportPayChkFileRequest, HighReimportPayChkFileResponse> {

}
