package com.howbuy.tms.high.batch.service.common.utils;

import com.howbuy.tms.high.batch.service.config.ThreadPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Description:基础任务执行类
 * @Author: yun.lu
 * Date: 2024/7/5 11:19
 */
@Component
@Slf4j
public class HowBuyRunTaskUil {

    @Autowired
    @Qualifier(ThreadPoolConfig.HIGH_BATCH_DEFAULT_POOL)
    private ThreadPoolTaskExecutor defaultPoolExecutor;

    @Autowired
    @Qualifier(ThreadPoolConfig.HIGH_BATCH_IO_POOL)
    private ThreadPoolTaskExecutor ioPoolExecutor;

    @Autowired
    @Qualifier(ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL)
    private ThreadPoolTaskExecutor computePoolExecutor;

    /**
     * 根据线程池名称获取对应的线程池执行器
     *
     * @param poolName 线程池名称
     * @return 对应的线程池执行器
     */
    private ThreadPoolTaskExecutor getThreadPoolExecutor(String poolName) {
        if (poolName == null) {
            return defaultPoolExecutor;
        }

        switch (poolName) {
            case ThreadPoolConfig.HIGH_BATCH_IO_POOL:
                return ioPoolExecutor;
            case ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL:
                return computePoolExecutor;
            case ThreadPoolConfig.HIGH_BATCH_DEFAULT_POOL:
            default:
                return defaultPoolExecutor;
        }
    }

    /**
     * 批量执行异步任务
     *
     * @param taskList 任务列表
     * @param <E>      任务类型
     */
    public <E extends AbstractHowbuyBaseTask> void runTask(List<E> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(taskList.size());
        for (AbstractHowbuyBaseTask abstractHowbuyBaseTask : taskList) {
            abstractHowbuyBaseTask.setLatch(latch);
            // 根据任务的poolName选择对应的线程池
            ThreadPoolTaskExecutor executor = getThreadPoolExecutor(abstractHowbuyBaseTask.getPoolName());
            executor.execute(abstractHowbuyBaseTask);
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch await出现异常,线程终止");
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 执行单个异步任务
     *
     * @param task 异步任务
     * @param <E>  任务类型
     */
    public <E extends AbstractHowbuyBaseTask> void runTask(E task) {
        if (task == null) {
            return;
        }
        CountDownLatch latch = new CountDownLatch(1);
        task.setLatch(latch);
        // 根据任务的poolName选择对应的线程池
        ThreadPoolTaskExecutor executor = getThreadPoolExecutor(task.getPoolName());
        executor.execute(task);
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch await出现异常,线程终止");
            Thread.currentThread().interrupt();
        }
    }
}
