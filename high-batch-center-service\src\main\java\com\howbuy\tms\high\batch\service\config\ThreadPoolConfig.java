package com.howbuy.tms.high.batch.service.config;

import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description:线程池配置
 * @Author: yun.lu
 * Date: 2023/5/23 14:11
 */
@Configuration
public class ThreadPoolConfig {
    /**
     * 高端批处理服务默认处理线程池
     */
    public static final String HIGH_BATCH_DEFAULT_POOL = "HIGH_BATCH_DEFAULT_POOL";

    /**
     * 高端批处理服务IO密集型处理线程池
     */
    public static final String HIGH_BATCH_IO_POOL = "HIGH_BATCH_IO_POOL";


    /**
     * 高端批处理服务计算密集型处理线程池
     */
    public static final String HIGH_BATCH_COMPUTE_POOL = "HIGH_BATCH_COMPUTE_POOL";

    /**
     * 默认线程池
     */
    @Bean(name = HIGH_BATCH_DEFAULT_POOL)
    @Primary
    public ThreadPoolTaskExecutor threadPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(HIGH_BATCH_DEFAULT_POOL + Constant.UNDER_LINE);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }

    /**
     * IO密集型线程池
     *
     * @return
     */
    @Bean(name = HIGH_BATCH_IO_POOL)
    public ThreadPoolTaskExecutor ioPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum * 2);
        executor.setMaxPoolSize(4 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(HIGH_BATCH_IO_POOL + Constant.UNDER_LINE);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }

    /**
     * 计算密集型线程池
     */
    @Bean(name = HIGH_BATCH_COMPUTE_POOL)
    public ThreadPoolTaskExecutor computePoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum);
        executor.setMaxPoolSize(cpuCoreNum);
        executor.setQueueCapacity(200);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(HIGH_BATCH_COMPUTE_POOL + Constant.UNDER_LINE);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }
}
