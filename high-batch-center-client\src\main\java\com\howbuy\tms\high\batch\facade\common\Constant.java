/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.facade.common;

/**
 * @description:高端常量定义
 * <AUTHOR>
 * @date 2017年7月5日 上午11:22:52
 * @since JDK 1.6
 */
public class Constant {
    /**
     * 调度结果反馈的Key
     */
    public static final String QRTZ_LOG_LIST_KEY = "qrtz_log_list";

    private Constant() {
    }

    /**
     * 系统操作员
     */
    public final static String OPERATOR_SYS = "system";

    /**
     * 私募文件编码
     */
    public static final String FUND_FILE_ENCODING = "UTF-8";
    /**
     * 高端公募文件编码
     */
    public static final String HIGH_FUND_FILE_ENCODING = "GBK";

    /**
     * 交易对账完成
     */
    public static String TRADE_CHECK_STATUS_COMPLETE = "1";

    /**
     * 公募文件处理记录表.文件操作类型 1-生成
     */
    public final static String FILE_OPTION_GENERATE = "1";
    /**
     * 公募文件处理记录表.文件操作类型 2-导入
     */
    public final static String FILE_OPTION_IMPORT = "2";
    // /文件操作状态
    /**
     * 公募文件处理记录表.文件操作状态 0-未处理
     */
    public final static String FILE_OP_STATUS_NOT_PROCESS = "0";
    /**
     * 公募文件处理记录表.文件操作状态 1-导入成功
     */
    public final static String FILE_OP_STATUS_IMPORT_SUCC = "1";
    /**
     * 公募文件处理记录表.文件操作状态 2-导入失败
     */
    public final static String FILE_OP_STATUS_IMPORT_FAIL = "2";
    /**
     * 公募文件处理记录表.文件操作状态 3-生成成功
     */
    public final static String FILE_OP_STATUS_GEN_SUCC = "3";
    /**
     * 公募文件处理记录表.文件操作状态 4-生成失败
     */
    public final static String FILE_OP_STATUS_GEN_FAIL = "4";
    /**
     * 公募文件处理记录表.文件操作状态 5-处理中
     */
    public final static String FILE_OP_STATUS_PROCESSING = "5";
    // / 处理状态
    /**
     * 公募文件处理记录表.处理状态 0-未处理
     */
    public final static String FILE_PROCESS_STATUS_NOT_PROCESS = "0";
    /**
     * 公募文件处理记录表.处理状态 1-通知成功
     */
    public final static String FILE_PROCESS_STATUS_NOTIFY_SUCC = "1";
    /**
     * 公募文件处理记录表.处理状态 2-处理中
     */
    public final static String FILE_PROCESS_STATUS_PROCESSING = "2";
    /**
     * 公募文件处理记录表.处理状态 3-处理成功
     */
    public final static String FILE_PROCESS_STATUS_PROCESS_SUCC = "3";
    /**
     * 公募文件处理记录表.处理状态 4-处理失败
     */
    public final static String FILE_PROCESS_STATUS_PROCESS_FAIL = "4";

    /**
     * 调度日志ID名
     */
    public static String QRTZ_LOG_ID = "qrtz_log_id";

    /**
     * 复核标记，0-未复核，1-已复核
     */
    public final static String CHECK_FLAG_NO = "0";
    public final static String CHECK_FLAG_YES = "1";

    /**
     * 高端公募交易申请日终消息
     */
    public final static String MSG_TRADE_APP_DAY_END = "1";
    /**
     * 高端公募日终退款消息
     */
    public final static String MSG_REFUND_FILE = "2";
    /**
     * 高端申请成功文件
     */
    public final static String MSG_APP_SUCCESS_FILE = "4";

    /**
     * 高端批处理系统码
     */
    public final static String HIGH_BATCH_SYS_CODE = "90";

    public static final String ASSET_CERTIFICATE_STATUS_VALID = "1";

    public static final String CXG_FILE_PATH_ACTINAME = "TPSMFF";
    public static final String CXG_FILE_NAME_PREFIX = "TPSMXF";


    /**
     * TA代码通配
     */
    public static final String ALL_TA_CODE = "XX";

    /**
     * HOWBUYCM-好买直销
     */
    public static final String HOWBUY_CM_TA = "HOWBUYCM";

    public static final String UNDER_LINE = "_";


    /**
     * 文件sdk key
     */
    public static final String BUSINESS_CODE = "businessCode";
    /**
     * 中间路径
     */
    public static final String MIDDLE_PATH = "middlePath";
    /**
     * 文件名
     */
    public static final String FILE_NAME = "fileName";

}
