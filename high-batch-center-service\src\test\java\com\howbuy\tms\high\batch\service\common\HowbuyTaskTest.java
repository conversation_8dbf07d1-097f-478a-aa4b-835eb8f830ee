package com.howbuy.tms.high.batch.service.common;

import com.howbuy.tms.high.batch.service.base.BaseTestSuite;
import com.howbuy.tms.high.batch.service.common.utils.TaskExecutionExample;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2025/7/23 15:27
 */
public class HowbuyTaskTest extends BaseTestSuite {
    @Autowired
    TaskExecutionExample taskExecutionExample;

    @Test
    public void test(){
        taskExecutionExample.demonstrateBatchExecution();
        taskExecutionExample.demonstrateMixedExecution();
        taskExecutionExample.demonstrateSingleExecution();
    }


}
