package com.howbuy.tms.high.batch.service.common.utils;

import com.howbuy.tms.high.batch.service.config.ThreadPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 任务执行示例类，演示如何使用不同线程池执行任务
 * @Author: developer
 * @Date: 2025/01/23
 */
@Service
@Slf4j
public class TaskExecutionExample {
    
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    
    /**
     * 演示批量执行不同类型的任务
     */
    public void demonstrateBatchExecution() {
        log.info("开始演示批量任务执行...");
        
        List<AbstractHowbuyBaseTask> taskList = new ArrayList<>();
        
        // 添加IO密集型任务
        taskList.add(new ExampleTask("IO任务1", "读取文件A", ThreadPoolConfig.HIGH_BATCH_IO_POOL));
        taskList.add(new ExampleTask("IO任务2", "网络请求B", ThreadPoolConfig.HIGH_BATCH_IO_POOL));
        
        // 添加计算密集型任务
        taskList.add(new ExampleTask("计算任务1", "数据处理C", ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL));
        taskList.add(new ExampleTask("计算任务2", "算法运算D", ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL));
        
        // 添加默认任务
        taskList.add(new ExampleTask("默认任务1", "常规处理E", ThreadPoolConfig.HIGH_BATCH_DEFAULT_POOL));
        taskList.add(new ExampleTask("默认任务2", "业务逻辑F", null)); // null会使用默认线程池
        
        // 执行批量任务
        long startTime = System.currentTimeMillis();
        howBuyRunTaskUil.runTask(taskList);
        long endTime = System.currentTimeMillis();
        
        log.info("批量任务执行完成，耗时: {}ms", endTime - startTime);
    }
    
    /**
     * 演示单个任务执行
     */
    public void demonstrateSingleExecution() {
        log.info("开始演示单个任务执行...");
        
        // 执行IO密集型任务
        ExampleTask ioTask = new ExampleTask("单个IO任务", "数据库查询", ThreadPoolConfig.HIGH_BATCH_IO_POOL);
        howBuyRunTaskUil.runTask(ioTask);
        
        // 执行计算密集型任务
        ExampleTask computeTask = new ExampleTask("单个计算任务", "复杂计算", ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL);
        howBuyRunTaskUil.runTask(computeTask);
        
        log.info("单个任务执行完成");
    }
    
    /**
     * 演示混合任务类型的批量执行
     */
    public void demonstrateMixedExecution() {
        log.info("开始演示混合任务类型的批量执行...");
        
        List<AbstractHowbuyBaseTask> mixedTasks = new ArrayList<>();
        
        // 创建不同类型的任务混合列表
        for (int i = 0; i < 10; i++) {
            String poolName;
            String taskType;
            
            if (i % 3 == 0) {
                poolName = ThreadPoolConfig.HIGH_BATCH_IO_POOL;
                taskType = "IO";
            } else if (i % 3 == 1) {
                poolName = ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL;
                taskType = "计算";
            } else {
                poolName = ThreadPoolConfig.HIGH_BATCH_DEFAULT_POOL;
                taskType = "默认";
            }
            
            mixedTasks.add(new ExampleTask(
                taskType + "任务" + i, 
                "数据" + i, 
                poolName
            ));
        }
        
        // 执行混合任务
        long startTime = System.currentTimeMillis();
        howBuyRunTaskUil.runTask(mixedTasks);
        long endTime = System.currentTimeMillis();
        
        log.info("混合任务执行完成，总任务数: {}, 耗时: {}ms", mixedTasks.size(), endTime - startTime);
    }
}
