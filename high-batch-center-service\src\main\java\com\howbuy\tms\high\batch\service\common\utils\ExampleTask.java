package com.howbuy.tms.high.batch.service.common.utils;

import com.howbuy.tms.high.batch.service.config.ThreadPoolConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 示例任务类，演示如何使用不同的线程池
 * @Author: developer
 * @Date: 2025/01/23
 */
@Slf4j
public class ExampleTask extends AbstractHowbuyBaseTask {
    
    private final String taskName;
    private final String data;
    
    /**
     * 构造函数
     * @param taskName 任务名称
     * @param data 任务数据
     * @param poolName 线程池名称
     */
    public ExampleTask(String taskName, String data, String poolName) {
        this.taskName = taskName;
        this.data = data;
        this.setPoolName(poolName);
    }
    
    @Override
    protected void callTask() {
        log.info("执行任务: {}, 数据: {}, 线程池: {}, 当前线程: {}", 
                taskName, data, getPoolName(), Thread.currentThread().getName());
        
        // 模拟不同类型的任务
        if (ThreadPoolConfig.HIGH_BATCH_IO_POOL.equals(getPoolName())) {
            // IO密集型任务模拟
            simulateIOTask();
        } else if (ThreadPoolConfig.HIGH_BATCH_COMPUTE_POOL.equals(getPoolName())) {
            // 计算密集型任务模拟
            simulateComputeTask();
        } else {
            // 默认任务模拟
            simulateDefaultTask();
        }
        
        log.info("任务完成: {}", taskName);
    }
    
    private void simulateIOTask() {
        try {
            // 模拟IO操作，如文件读写、网络请求等
            Thread.sleep(100);
            log.info("IO任务执行中...");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("IO任务被中断", e);
        }
    }
    
    private void simulateComputeTask() {
        // 模拟CPU密集型计算
        long result = 0;
        for (int i = 0; i < 1000000; i++) {
            result += i;
        }
        log.info("计算任务完成，结果: {}", result);
    }
    
    private void simulateDefaultTask() {
        try {
            // 模拟一般任务
            Thread.sleep(50);
            log.info("默认任务执行中...");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("默认任务被中断", e);
        }
    }
}
